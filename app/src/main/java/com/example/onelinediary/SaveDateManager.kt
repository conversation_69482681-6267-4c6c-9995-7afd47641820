package com.example.onelinediary

import android.content.Context
import android.content.SharedPreferences
import android.os.Environment
import android.util.Log
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Manages the save dates for different content types in the app
 */
class SaveDateManager(private val context: Context) {
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    /**
     * Records that content of the specified type was saved today
     */
    fun recordSave(contentType: ContentType) {
        val today = dateFormat.format(Date())
        prefs.edit().putString(contentType.key, today).apply()

        // Also add this date to the set of all dates with content
        val allDates = getAllSaveDates().toMutableSet()
        allDates.add(today)
        prefs.edit().putStringSet(ALL_SAVE_DATES_KEY, allDates).apply()

        // Log for debugging
        Log.d("SaveDateManager", "Recorded save for ${contentType.name} on date: $today")
        Log.d("SaveDateManager", "Updated all save dates: $allDates")

        // Also save in diary_prefs for redundancy
        val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
        diaryPrefs.edit().putString("last_save_date_${contentType.name.lowercase()}", today).apply()

        // Verify that the date was added to the set
        val verifyDates = getAllSaveDates()
        if (!verifyDates.contains(today)) {
            Log.e("SaveDateManager", "Failed to add date $today to saved dates set! Current set: $verifyDates")

            // Try again with a different approach
            val editor = prefs.edit()
            editor.remove(ALL_SAVE_DATES_KEY)
            editor.apply()

            editor.putStringSet(ALL_SAVE_DATES_KEY, allDates)
            editor.apply()

            // Verify again
            val secondVerify = getAllSaveDates()
            Log.d("SaveDateManager", "After second attempt, saved dates: $secondVerify")
        }
    }

    /**
     * Checks if content of the specified type has already been saved today
     */
    fun hasSavedToday(contentType: ContentType): Boolean {
        val lastSaveDate = prefs.getString(contentType.key, "") ?: ""
        val today = dateFormat.format(Date())
        return lastSaveDate == today
    }

    /**
     * Checks if any content has been saved on the specified date
     */
    fun hasContentOnDate(date: Calendar): Boolean {
        val dateString = dateFormat.format(date.time)
        Log.d("SaveDateManager", "Checking if date $dateString has content")

        // First check if the date is in our saved dates set
        if (getAllSaveDates().contains(dateString)) {
            Log.d("SaveDateManager", "Date $dateString is in saved dates set")
            return true
        }

        // Check shared preferences (diary_prefs)
        val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
        val hasText = diaryPrefs.contains("text_$dateString")
        val hasPhoto = diaryPrefs.contains("photo_$dateString")
        val hasVideo = diaryPrefs.contains("video_$dateString")
        val hasAudio = diaryPrefs.contains("audio_$dateString")
        val hasMood = diaryPrefs.contains("mood_$dateString")

        val hasContentInPrefs = hasText || hasPhoto || hasVideo || hasAudio || hasMood

        if (hasContentInPrefs) {
            Log.d("SaveDateManager", "Found content for date $dateString in shared preferences")

            // Add this date to the saved dates set
            val allDates = getAllSaveDates().toMutableSet()
            allDates.add(dateString)
            prefs.edit().putStringSet(ALL_SAVE_DATES_KEY, allDates).apply()
            return true
        }

        // Check if there are files for this date in the OnLiDi folder
        val folderName = "OnLiDi"
        val folder = File(Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS), folderName)

        if (folder.exists()) {
            val files = folder.listFiles()
            if (files != null) {
                for (file in files) {
                    if (file.name.startsWith(dateString)) {
                        Log.d("SaveDateManager", "Found file for date $dateString: ${file.name}")

                        // Add this date to the saved dates set
                        val allDates = getAllSaveDates().toMutableSet()
                        allDates.add(dateString)
                        prefs.edit().putStringSet(ALL_SAVE_DATES_KEY, allDates).apply()

                        return true
                    }
                }
            }
        }

        // Check app's external files directory as a last resort
        Log.d("SaveDateManager", "Checking app's external files directory for date $dateString")

        // Check for photos
        val picturesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        if (picturesDir != null && picturesDir.exists()) {
            val pictureFiles = picturesDir.listFiles()
            if (pictureFiles != null) {
                for (file in pictureFiles) {
                    if (file.name.contains(dateString.replace("-", ""))) {
                        Log.d("SaveDateManager", "Found picture file for date $dateString: ${file.name}")

                        // Add this date to the saved dates set
                        val allDates = getAllSaveDates().toMutableSet()
                        allDates.add(dateString)
                        prefs.edit().putStringSet(ALL_SAVE_DATES_KEY, allDates).apply()

                        return true
                    }
                }
            }
        }



        Log.d("SaveDateManager", "No content found for date $dateString")
        return false
    }

    /**
     * Gets all dates that have content saved
     */
    fun getAllSaveDates(): Set<String> {
        // Get the saved dates from preferences
        val savedDates = prefs.getStringSet(ALL_SAVE_DATES_KEY, emptySet()) ?: emptySet()

        // If we have no saved dates, try to scan the file system to rebuild the list
        if (savedDates.isEmpty()) {
            Log.d("SaveDateManager", "No saved dates found in preferences, scanning file system")
            return scanFileSystemForDates()
        }

        return savedDates
    }

    /**
     * Scans the file system to find all dates with content
     * This is a fallback mechanism if the saved dates set is empty
     */
    private fun scanFileSystemForDates(): Set<String> {
        val result = mutableSetOf<String>()

        // Check the OnLiDi folder in Documents
        val folderName = "OnLiDi"
        val folder = File(Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS), folderName)

        if (folder.exists()) {
            Log.d("SaveDateManager", "Scanning folder: ${folder.absolutePath}")

            // Get all files in the folder
            val files = folder.listFiles()
            if (files != null) {
                for (file in files) {
                    try {
                        // Extract date from filename (format: yyyy-MM-dd_contenttype.txt)
                        val fileName = file.name
                        if (fileName.matches(Regex("\\d{4}-\\d{2}-\\d{2}.*\\.txt"))) {
                            val dateStr = fileName.substring(0, 10) // Extract yyyy-MM-dd
                            result.add(dateStr)
                            Log.d("SaveDateManager", "Found date from file: $dateStr")
                        }
                    } catch (e: Exception) {
                        Log.e("SaveDateManager", "Error parsing filename: ${file.name}", e)
                    }
                }
            }
        }

        // Check shared preferences for dates
        val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
        val allPrefs = diaryPrefs.all

        for (key in allPrefs.keys) {
            try {
                // Look for keys with format "text_yyyy-MM-dd", "photo_yyyy-MM-dd", etc.
                if (key.contains("_")) {
                    val parts = key.split("_")
                    if (parts.size >= 2) {
                        val dateStr = parts[1]
                        if (dateStr.matches(Regex("\\d{4}-\\d{2}-\\d{2}"))) {
                            result.add(dateStr)
                            Log.d("SaveDateManager", "Found date from shared preferences: $dateStr")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("SaveDateManager", "Error parsing shared preference key: $key", e)
            }
        }

        // Check app's external files directories

        // Check Pictures directory for photos
        val picturesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        if (picturesDir != null && picturesDir.exists()) {
            val pictureFiles = picturesDir.listFiles()
            if (pictureFiles != null) {
                for (file in pictureFiles) {
                    try {
                        // Look for date pattern in filename (yyyyMMdd)
                        val datePattern = Regex("(\\d{8})")
                        val matchResult = datePattern.find(file.name)
                        if (matchResult != null) {
                            val dateStr = matchResult.value
                            // Convert yyyyMMdd to yyyy-MM-dd
                            val formattedDate = "${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}"
                            result.add(formattedDate)
                            Log.d("SaveDateManager", "Found date from picture file: $formattedDate")
                        }
                    } catch (e: Exception) {
                        Log.e("SaveDateManager", "Error parsing picture filename: ${file.name}", e)
                    }
                }
            }
        }



        // Save the reconstructed dates to preferences
        if (result.isNotEmpty()) {
            Log.d("SaveDateManager", "Saving ${result.size} reconstructed dates to preferences")
            prefs.edit().putStringSet(ALL_SAVE_DATES_KEY, result).apply()
        }

        return result
    }

    /**
     * Gets all content saved on a specific date
     */
    fun getContentForDate(date: Calendar): Map<ContentType, String> {
        val dateString = dateFormat.format(date.time)
        val result = mutableMapOf<ContentType, String>()

        Log.d("SaveDateManager", "Getting content for date: $dateString")

        // First check if this date has any content
        val hasContent = hasContentOnDate(date)
        Log.d("SaveDateManager", "Date $dateString has content: $hasContent")

        // If no content, return empty map immediately
        if (!hasContent) {
            Log.d("SaveDateManager", "No content found for date $dateString, returning empty map")
            return emptyMap()
        }

        // First check shared preferences (diary_prefs) as it's faster
        val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)

        // Check for each content type in shared preferences
        val textContent = diaryPrefs.getString("text_$dateString", null)
        if (textContent != null) {
            result[ContentType.TEXT] = textContent
            Log.d("SaveDateManager", "Found TEXT content in shared preferences: $textContent")
        } else {
            // If text content is not in shared preferences, check if there's a text file
            val folderName = "OnLiDi"
            val folder = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOCUMENTS), folderName)

            if (folder.exists()) {
                val textFile = File(folder, "${dateString}_text.txt")
                if (textFile.exists()) {
                    try {
                        val content = textFile.readText()
                        result[ContentType.TEXT] = content
                        Log.d("SaveDateManager", "Found TEXT content in file: $content")
                    } catch (e: Exception) {
                        Log.e("SaveDateManager", "Error reading text file", e)
                    }
                }
            }
        }

        val photoContent = diaryPrefs.getString("photo_$dateString", null)
        if (photoContent != null) {
            result[ContentType.PHOTO] = photoContent
            Log.d("SaveDateManager", "Found PHOTO content in shared preferences: $photoContent")
        }



        val moodContent = diaryPrefs.getString("mood_$dateString", null)
        if (moodContent != null) {
            result[ContentType.MOOD] = moodContent
            Log.d("SaveDateManager", "Found MOOD content in shared preferences: $moodContent")
        }

        // If we found content in shared preferences, return it
        if (result.isNotEmpty()) {
            Log.d("SaveDateManager", "Returning content from shared preferences: $result")
            return result
        }

        // Check the OnLiDi folder for files with this date
        val folderName = "OnLiDi"
        val folder = File(Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS), folderName)

        if (folder.exists()) {
            Log.d("SaveDateManager", "Looking for content files for date: $dateString in folder: ${folder.absolutePath}")

            // List all files in the folder for debugging
            val allFiles = folder.listFiles()
            if (allFiles != null) {
                Log.d("SaveDateManager", "All files in folder: ${allFiles.map { it.name }}")

                // First, specifically look for text files
                val textFile = File(folder, "${dateString}_text.txt")
                if (textFile.exists() && !result.containsKey(ContentType.TEXT)) {
                    try {
                        val content = textFile.readText()
                        result[ContentType.TEXT] = content
                        Log.d("SaveDateManager", "Found TEXT content in specific file: $content")
                    } catch (e: Exception) {
                        Log.e("SaveDateManager", "Error reading text file", e)
                    }
                }

                // Check for files that match this date
                for (file in allFiles) {
                    if (file.name.startsWith(dateString)) {
                        Log.d("SaveDateManager", "Found file for date $dateString: ${file.name}")

                        try {
                            val content = file.readText()

                            // Determine content type from filename
                            when {
                                file.name.contains("_text.txt") -> {
                                    if (!result.containsKey(ContentType.TEXT)) {
                                        result[ContentType.TEXT] = content
                                        Log.d("SaveDateManager", "Found TEXT content in file: $content")
                                    }
                                }
                                file.name.contains("_photo.txt") -> {
                                    result[ContentType.PHOTO] = content
                                    Log.d("SaveDateManager", "Found PHOTO content in file: $content")
                                }

                                file.name.contains("_mood.txt") -> {
                                    result[ContentType.MOOD] = content
                                    Log.d("SaveDateManager", "Found MOOD content in file: $content")
                                }
                                // For old format files (just the date)
                                file.name == "$dateString.txt" -> {
                                    val fileContent = content
                                    when {
                                        fileContent.startsWith("Photo") -> {
                                            result[ContentType.PHOTO] = fileContent
                                            Log.d("SaveDateManager", "Found PHOTO content in old format file: $fileContent")
                                        }

                                        fileContent.startsWith("Mood") -> {
                                            result[ContentType.MOOD] = fileContent
                                            Log.d("SaveDateManager", "Found MOOD content in old format file: $fileContent")
                                        }
                                        else -> {
                                            if (!result.containsKey(ContentType.TEXT)) {
                                                result[ContentType.TEXT] = fileContent
                                                Log.d("SaveDateManager", "Found TEXT content in old format file: $fileContent")
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("SaveDateManager", "Error reading file ${file.name}", e)
                        }
                    }
                }
            } else {
                Log.d("SaveDateManager", "No files found in folder or unable to list files")
            }

            if (result.isEmpty()) {
                Log.d("SaveDateManager", "No content found for date $dateString in files or shared preferences")
            } else {
                Log.d("SaveDateManager", "Returning content from files: $result")
            }
        } else {
            Log.d("SaveDateManager", "Folder does not exist: ${folder.absolutePath}")

            // Try to create the folder
            try {
                val created = folder.mkdirs()
                Log.d("SaveDateManager", "Attempted to create folder: $created")
            } catch (e: Exception) {
                Log.e("SaveDateManager", "Error creating folder", e)
            }
        }

        // Also check app's external files directory as a last resort
        if (result.isEmpty()) {
            Log.d("SaveDateManager", "Checking app's external files directory")

            // Check Pictures directory for photos
            val picturesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            if (picturesDir != null && picturesDir.exists()) {
                val pictureFiles = picturesDir.listFiles()
                if (pictureFiles != null) {
                    for (file in pictureFiles) {
                        if (file.name.contains(dateString.replace("-", ""))) {
                            Log.d("SaveDateManager", "Found picture file for date $dateString: ${file.name}")
                            val uri = "file://${file.absolutePath}"
                            result[ContentType.PHOTO] = "Photo captured with camera: $uri"
                            Log.d("SaveDateManager", "Added photo URI: $uri")
                            break
                        }
                    }
                }
            }


        }

        // If we still have no content but hasContentOnDate returned true,
        // add a fallback text entry to ensure something is displayed
        if (result.isEmpty()) {
            Log.d("SaveDateManager", "No content found in any location, adding fallback text entry")
            result[ContentType.TEXT] = "Entry for $dateString"
        }

        return result
    }

    /**
     * Finds the next date with content after the given date
     * Returns null if no next date with content is found
     */
    fun findNextDateWithContent(currentDate: Calendar): Calendar? {
        val allDates = getAllSaveDates()
        if (allDates.isEmpty()) return null

        // Convert all string dates to Calendar objects and sort them
        val allCalendarDates = allDates.mapNotNull { dateString ->
            try {
                val date = Calendar.getInstance()
                date.time = dateFormat.parse(dateString) ?: return@mapNotNull null
                date
            } catch (e: Exception) {
                Log.e("SaveDateManager", "Error parsing date: $dateString", e)
                null
            }
        }.sortedBy { it.timeInMillis }

        // Find the next date after the current date
        val currentDateString = dateFormat.format(currentDate.time)
        return allCalendarDates.find {
            val dateString = dateFormat.format(it.time)
            dateString > currentDateString
        }
    }

    /**
     * Finds the previous date with content before the given date
     * Returns null if no previous date with content is found
     */
    fun findPreviousDateWithContent(currentDate: Calendar): Calendar? {
        val allDates = getAllSaveDates()
        if (allDates.isEmpty()) return null

        // Convert all string dates to Calendar objects and sort them
        val allCalendarDates = allDates.mapNotNull { dateString ->
            try {
                val date = Calendar.getInstance()
                date.time = dateFormat.parse(dateString) ?: return@mapNotNull null
                date
            } catch (e: Exception) {
                Log.e("SaveDateManager", "Error parsing date: $dateString", e)
                null
            }
        }.sortedByDescending { it.timeInMillis }

        // Find the previous date before the current date
        val currentDateString = dateFormat.format(currentDate.time)
        return allCalendarDates.find {
            val dateString = dateFormat.format(it.time)
            dateString < currentDateString
        }
    }

    /**
     * Clears all saved dates (for testing purposes)
     * This only clears the preferences, not the actual files
     */
    fun clearAllSaveDates() {
        prefs.edit().clear().apply()
    }

    /**
     * Clears only today's data while preserving past entries
     * This removes today's entries from preferences and deletes today's files
     */
    fun clearTodayData(): Boolean {
        val today = dateFormat.format(Date())
        Log.d("SaveDateManager", "Clearing only today's data ($today)")
        var success = true

        try {
            // 1. Remove today from the set of all saved dates
            val allDates = getAllSaveDates().toMutableSet()
            val hadTodayEntry = allDates.remove(today)
            prefs.edit().putStringSet(ALL_SAVE_DATES_KEY, allDates).apply()

            if (!hadTodayEntry) {
                Log.d("SaveDateManager", "No entry found for today in saved dates set")
            } else {
                Log.d("SaveDateManager", "Removed today from saved dates set")
            }

            // 2. Clear today's entries from shared preferences
            val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
            val editor = diaryPrefs.edit()

            // Remove today's entries for each content type
            editor.remove("text_$today")
            editor.remove("photo_$today")
            editor.remove("mood_$today")
            editor.apply()

            // Also clear the last save date for each content type if it's today
            for (contentType in ContentType.values()) {
                val lastSaveDate = prefs.getString(contentType.key, "")
                if (lastSaveDate == today) {
                    prefs.edit().remove(contentType.key).apply()
                    Log.d("SaveDateManager", "Cleared last save date for ${contentType.name}")
                }
            }

            Log.d("SaveDateManager", "Cleared today's entries from shared preferences")

            // 3. Delete today's files in the OnLiDi folder
            val folderName = "OnLiDi"
            val folder = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOCUMENTS), folderName)

            if (folder.exists()) {
                val files = folder.listFiles()
                if (files != null) {
                    for (file in files) {
                        if (file.name.startsWith(today)) {
                            val deleted = file.delete()
                            if (!deleted) {
                                Log.e("SaveDateManager", "Failed to delete today's file: ${file.absolutePath}")
                                success = false
                            } else {
                                Log.d("SaveDateManager", "Deleted today's file: ${file.absolutePath}")
                            }
                        }
                    }
                }
            }

            // 4. Delete today's media files in app's external files directories
            val todayWithoutDashes = today.replace("-", "")

            // Delete today's photos
            val picturesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            if (picturesDir != null && picturesDir.exists()) {
                val pictureFiles = picturesDir.listFiles()
                if (pictureFiles != null) {
                    for (file in pictureFiles) {
                        if (file.name.contains(todayWithoutDashes)) {
                            val deleted = file.delete()
                            if (!deleted) {
                                Log.e("SaveDateManager", "Failed to delete today's photo file: ${file.absolutePath}")
                                success = false
                            } else {
                                Log.d("SaveDateManager", "Deleted today's photo file: ${file.absolutePath}")
                            }
                        }
                    }
                }
            }

            // Delete today's videos
            val moviesDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
            if (moviesDir != null && moviesDir.exists()) {
                val movieFiles = moviesDir.listFiles()
                if (movieFiles != null) {
                    for (file in movieFiles) {
                        if (file.name.contains(todayWithoutDashes)) {
                            val deleted = file.delete()
                            if (!deleted) {
                                Log.e("SaveDateManager", "Failed to delete today's video file: ${file.absolutePath}")
                                success = false
                            } else {
                                Log.d("SaveDateManager", "Deleted today's video file: ${file.absolutePath}")
                            }
                        }
                    }
                }
            }

            // Delete today's audio
            val musicDir = context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)
            if (musicDir != null && musicDir.exists()) {
                val musicFiles = musicDir.listFiles()
                if (musicFiles != null) {
                    for (file in musicFiles) {
                        if (file.name.contains(todayWithoutDashes)) {
                            val deleted = file.delete()
                            if (!deleted) {
                                Log.e("SaveDateManager", "Failed to delete today's audio file: ${file.absolutePath}")
                                success = false
                            } else {
                                Log.d("SaveDateManager", "Deleted today's audio file: ${file.absolutePath}")
                            }
                        }
                    }
                }
            }

            Log.d("SaveDateManager", "Today's data reset complete. Success: $success")
            return success
        } catch (e: Exception) {
            Log.e("SaveDateManager", "Error clearing today's data", e)
            return false
        }
    }

    /**
     * Clears all data including preferences and files (for testing purposes)
     * This is a more comprehensive reset that deletes all saved files as well
     */
    fun clearAllData(): Boolean {
        Log.d("SaveDateManager", "Clearing all data (preferences and files)")
        var success = true

        try {
            // 1. Clear all shared preferences
            prefs.edit().clear().apply()

            // Also clear diary_prefs
            val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
            diaryPrefs.edit().clear().apply()

            Log.d("SaveDateManager", "Cleared all shared preferences")

            // 2. Delete all files in the OnLiDi folder
            val folderName = "OnLiDi"
            val folder = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOCUMENTS), folderName)

            if (folder.exists()) {
                val files = folder.listFiles()
                if (files != null) {
                    for (file in files) {
                        val deleted = file.delete()
                        if (!deleted) {
                            Log.e("SaveDateManager", "Failed to delete file: ${file.absolutePath}")
                            success = false
                        } else {
                            Log.d("SaveDateManager", "Deleted file: ${file.absolutePath}")
                        }
                    }
                }

                // Optionally delete the folder itself
                // folder.delete()
            }

            // 3. Delete all media files in app's external files directories

            // Delete photos
            val picturesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            if (picturesDir != null && picturesDir.exists()) {
                val pictureFiles = picturesDir.listFiles()
                if (pictureFiles != null) {
                    for (file in pictureFiles) {
                        val deleted = file.delete()
                        if (!deleted) {
                            Log.e("SaveDateManager", "Failed to delete photo file: ${file.absolutePath}")
                            success = false
                        } else {
                            Log.d("SaveDateManager", "Deleted photo file: ${file.absolutePath}")
                        }
                    }
                }
            }

            // Delete videos
            val moviesDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
            if (moviesDir != null && moviesDir.exists()) {
                val movieFiles = moviesDir.listFiles()
                if (movieFiles != null) {
                    for (file in movieFiles) {
                        val deleted = file.delete()
                        if (!deleted) {
                            Log.e("SaveDateManager", "Failed to delete video file: ${file.absolutePath}")
                            success = false
                        } else {
                            Log.d("SaveDateManager", "Deleted video file: ${file.absolutePath}")
                        }
                    }
                }
            }

            // Delete audio
            val musicDir = context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)
            if (musicDir != null && musicDir.exists()) {
                val musicFiles = musicDir.listFiles()
                if (musicFiles != null) {
                    for (file in musicFiles) {
                        val deleted = file.delete()
                        if (!deleted) {
                            Log.e("SaveDateManager", "Failed to delete audio file: ${file.absolutePath}")
                            success = false
                        } else {
                            Log.d("SaveDateManager", "Deleted audio file: ${file.absolutePath}")
                        }
                    }
                }
            }

            Log.d("SaveDateManager", "Data reset complete. Success: $success")
            return success
        } catch (e: Exception) {
            Log.e("SaveDateManager", "Error clearing all data", e)
            return false
        }
    }

    /**
     * Gets dates with content within a specific time period
     * @param timePeriod The time period to filter by (DAY, WEEK, MONTH, YEAR, ALL)
     * @return A set of date strings in the format "yyyy-MM-dd" that fall within the specified time period
     */
    fun getDatesInTimePeriod(timePeriod: DownloadTimePeriod): Set<String> {
        val allDates = getAllSaveDates()
        if (allDates.isEmpty() || timePeriod == DownloadTimePeriod.ALL) {
            return allDates
        }

        val today = Calendar.getInstance()
        val filteredDates = mutableSetOf<String>()

        // Convert string dates to Calendar objects for comparison
        for (dateString in allDates) {
            try {
                val date = Calendar.getInstance()
                date.time = dateFormat.parse(dateString) ?: continue

                // Check if the date falls within the specified time period
                when (timePeriod) {
                    DownloadTimePeriod.DAY -> {
                        // Only include today's date
                        val todayString = dateFormat.format(today.time)
                        if (dateString == todayString) {
                            filteredDates.add(dateString)
                        }
                    }
                    DownloadTimePeriod.WEEK -> {
                        // Include dates from the past 7 days
                        val weekAgo = Calendar.getInstance()
                        weekAgo.add(Calendar.DAY_OF_YEAR, -7)
                        if (date.after(weekAgo) || date.timeInMillis == weekAgo.timeInMillis) {
                            filteredDates.add(dateString)
                        }
                    }
                    DownloadTimePeriod.MONTH -> {
                        // Include dates from the past 30 days
                        val monthAgo = Calendar.getInstance()
                        monthAgo.add(Calendar.DAY_OF_YEAR, -30)
                        if (date.after(monthAgo) || date.timeInMillis == monthAgo.timeInMillis) {
                            filteredDates.add(dateString)
                        }
                    }
                    DownloadTimePeriod.YEAR -> {
                        // Include dates from the past 365 days
                        val yearAgo = Calendar.getInstance()
                        yearAgo.add(Calendar.DAY_OF_YEAR, -365)
                        if (date.after(yearAgo) || date.timeInMillis == yearAgo.timeInMillis) {
                            filteredDates.add(dateString)
                        }
                    }
                    DownloadTimePeriod.ALL -> {
                        // Already handled above
                    }
                }
            } catch (e: Exception) {
                Log.e("SaveDateManager", "Error parsing date: $dateString", e)
            }
        }

        return filteredDates
    }

    companion object {
        private const val PREFS_NAME = "one_line_diary_prefs"
        private const val ALL_SAVE_DATES_KEY = "all_save_dates"
    }
}

/**
 * Represents the different types of content that can be saved
 */
enum class ContentType(val key: String) {
    TEXT("last_text_save_date"),
    PHOTO("last_photo_save_date"),
    MOOD("last_mood_save_date")
}

/**
 * Represents the different time periods for downloading content
 */
enum class DownloadTimePeriod {
    DAY,    // Today only
    WEEK,   // Last 7 days
    MONTH,  // Last 30 days
    YEAR,   // Last 365 days
    ALL     // All dates
}
